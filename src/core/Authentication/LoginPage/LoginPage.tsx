import BroadcastChannel from 'broadcast-channel';
import { find, get, startsWith } from 'lodash';
import React, { useCallback, useContext, useEffect, useState } from 'react';
import { useApolloClient, useMutation } from 'react-apollo';
import {
  Redirect,
  Route,
  Switch,
  useHistory,
  useRouteMatch,
} from 'react-router-dom';

import { TenantInfoContext } from '../../../common/components/containers/TenantInfoContextProvider';
import useT from '../../../common/components/utils/Translations/useT';
import currentUserGql from '../../../common/data/withCurrentUser.graphql';
import { getRestrictedModules } from '../../../common/data/withModules/useModules';
import { handleFormErrors } from '../../../common/errors';
import Header from '../../../common/layouts/AuthenticationLayout/Header';
import continueLoginGql from './data/continueLogin.graphql';
import initiateLoginGql from './data/initiateLogin.graphql';
import confirmTermsConditionsGql from './data/confirmTermsConditions.graphql';
import systemConfigGql from './data/systemConfig.graphql';
import InactiveAllocationsView from './InactiveAllocationsView';
import LoginConfirmationForm from './LoginConfirmationForm';
import LoginForm from './LoginForm';
import PasswordExpirationView from './PasswordExpirationView';
import LoginTermsConditionsModal from './LoginTermsConditionsModal';
import IPersonTermsConditions from './abstract/IPersonTermsConditions';
import TcUserAgreement from './abstract/TcUserAgreement';
import useFilterStore from '../../../common/components/controls/FilterBar/hooks/useFilterStore';

const LOGIN_BROADCAST_CHANNEL = 'login';
const LISTENER_TYPE = 'message';
const LOGGED_MESSAGE = 'logged';
const LOGGED_OUT_MESSAGE = 'loggedOut';

const LoginPage: React.FC<{
  completing: boolean;
  inviteOperationId?: number;
  passwordResetOperationId?: number;
}> = ({ completing = false, inviteOperationId, passwordResetOperationId }) => {
  const { siteName, tenantLogo, tenantId, loginPageMessage } = useContext(
    TenantInfoContext,
  );

  const t = useT();
  const history = useHistory();
  const match = useRouteMatch();
  const apolloClient = useApolloClient();

  const [isDisabled, setIsDisabled] = useState(completing);
  const [temporaryUserId, setTemporaryUserId] = useState(null);
  const [formError, setFormError] = useState(null);
  const [pendingTokenConfirmation, setPendingTokenConfirmation] = useState(
    false,
  );
  const [termsConditionsInfo, setTermsConditionsInfo] = useState<{
    tcConfirmationToken: string;
    termsConditions: IPersonTermsConditions[];
  }>();

  const [initiateLogin] = useMutation(initiateLoginGql, {
    update: (proxy, mutationResult) =>
      updateCache('initiateLogin', mutationResult),
  });
  const [continueLogin] = useMutation(continueLoginGql, {
    update: (proxy, mutationResult) =>
      updateCache('continueLogin', mutationResult),
  });

  const [confirmTermsConditions] = useMutation(confirmTermsConditionsGql, {
    update: (proxy, mutationResult) =>
      updateCache('confirmTermsConditions', mutationResult),
  });

  const updateCache = useCallback(
    async (dataField, { data }) => {
      const user = data?.[dataField].user;

      if (user) {
        const readRes = await apolloClient.readQuery<{
          systemConfig: object;
        }>({
          query: systemConfigGql,
        });

        await apolloClient.writeQuery({
          query: currentUserGql,
          data: { me: user, systemConfig: get(readRes, 'systemConfig') },
        });
      }
    },
    [apolloClient],
  );

  const loginChannel = new BroadcastChannel(LOGIN_BROADCAST_CHANNEL);

  const loginEventListener = useCallback(
    e => {
      if (!isDisabled && e) {
        const { tenantId: userTenantId, message, path, user } = e;

        if (message === LOGGED_MESSAGE && userTenantId === tenantId) {
          window.location.pathname = getLastPath(user);
        }
      }
    },
    [isDisabled, tenantId],
  );

  useEffect(() => {
    loginChannel.addEventListener(LISTENER_TYPE, loginEventListener);

    return () => {
      loginChannel &&
        loginChannel.removeEventListener(LISTENER_TYPE, loginEventListener);
    };
  }, [loginChannel, loginEventListener]);

  const runLoginOperation = useCallback(
    async (operation, processError = false) => {
      setIsDisabled(true);

      try {
        const res = await handleFormErrors(operation(), t);
        const tenantId = res?.user?.tenantId;
        if (tenantId) {
          loginChannel.postMessage({
            message: LOGGED_MESSAGE,
            tenantId,
            path: getLastPath(res?.user),
            user: res?.user,
          });
        }

        return res;
      } catch (error) {
        console.error(error);
        setIsDisabled(false);
        if (!processError) {
          throw error;
        }

        const {
          errors: { _error: formError },
        } = error;

        setFormError(formError);
      }
    },
    [loginChannel, t],
  );

  const { onChange: onContentMoveFilterChange } = useFilterStore(
    'CONTENT_MOVE_FILTERS',
    {
      skip: true,
    },
  );
  const { onChange: onConversationsListFilterChange } = useFilterStore(
    'CONVERSATIONS_LIST',
    {
      skip: true,
    },
  );

  const finalizeLogin = useCallback(
    ({
      credentialsExpired,
      inactiveAllocations,
      user,
      temporaryUserId: loginTemporaryUserId,
      termsConditions,
      tcConfirmationToken,
      pendingEmailConfirmation,
      pendingTcConfirmation,
    }) => {
      if (user) {
        onContentMoveFilterChange({ resources: undefined });
        onConversationsListFilterChange({
          searchQuery: '',
          searchQueryMode: undefined,
          history: undefined,
        });
        const path = getLastPath(user);
        history.push(path);
        setIsDisabled(false);
        return user;
      }

      if (pendingEmailConfirmation) {
        setPendingTokenConfirmation(true);
      } else {
        setPendingTokenConfirmation(false);
      }
      if (termsConditions && termsConditions.length > 0) {
        setTermsConditionsInfo({
          termsConditions,
          tcConfirmationToken,
        });
      }
      if (pendingEmailConfirmation) {
        setTemporaryUserId(loginTemporaryUserId);
        setIsDisabled(false);
        history.push('/login/confirmation/email');
        return null;
      }

      if (credentialsExpired) {
        history.push('/login/expired');
        setIsDisabled(false);
        return null;
      }

      if (inactiveAllocations) {
        history.push('/login/inactive');
        setIsDisabled(false);
        return null;
      }

      if (pendingTcConfirmation) {
        return null;
      }
    },
    [history],
  );

  const handleEmailPasswordLogin = useCallback(
    async ({ email, password, rememberMe }) => {
      const loginResult = await runLoginOperation(async () => {
        const result = await initiateLogin({
          variables: {
            email,
            password,
            rememberMe,
            inviteOperationId,
            passwordResetOperationId,
          },
        });
        return result.data.initiateLogin;
      }, true);

      return finalizeLogin(loginResult);
    },
    [runLoginOperation, initiateLogin, finalizeLogin],
  );

  const handleContinueLogin = useCallback(
    async ({ confirmationType, confirmationToken }) => {
      const loginResult = await runLoginOperation(async () => {
        const variables = {
          emailConfirmationToken: null,
          personId: temporaryUserId,
          inviteOperationId,
          passwordResetOperationId,
        };

        if (confirmationType === 'email') {
          variables.emailConfirmationToken = confirmationToken;
        }

        const result = await continueLogin({ variables });

        return result.data.continueLogin;
      });
      return finalizeLogin(loginResult);
    },
    [continueLogin, finalizeLogin, runLoginOperation, temporaryUserId],
  );

  const handleConfirmAgreements = useCallback(
    async ({
      tcAgreements,
      tcConfirmationToken,
    }: {
      tcAgreements: TcUserAgreement[];
      tcConfirmationToken: string;
    }) => {
      const loginResult = await runLoginOperation(async () => {
        const variables = {
          tcConfirmationToken,
          tcAgreements,
        };
        const result = await confirmTermsConditions({ variables });

        return result.data.confirmTermsConditions;
      });

      return finalizeLogin(loginResult);
    },
    [termsConditionsInfo],
  );

  const renderLoginForm = useCallback(
    ({ match: { params } }) => {
      loginChannel.postMessage({
        message: LOGGED_OUT_MESSAGE,
        tenantId,
      });

      return (
        <LoginForm
          confirmationToken={params?.confirmationToken}
          formError={formError}
          isDisabled={isDisabled}
          onSubmit={handleEmailPasswordLogin}
        />
      );
    },
    [handleEmailPasswordLogin, formError, isDisabled, loginChannel, tenantId],
  );

  const renderLoginConfirmation = useCallback(
    () => (
      <LoginConfirmationForm
        hasUserInfo={!!temporaryUserId}
        onSubmit={handleContinueLogin}
      />
    ),
    [handleContinueLogin, temporaryUserId],
  );

  return (
    <>
      <div id="ui-health-check" />
      <Header
        classNames="mb-30"
        logoSrc={tenantLogo}
        title={t('Login to #{siteName}', { siteName })}
      />
      <Switch>
        <Route exact path={match.url} render={renderLoginForm} />
        <Route
          exact
          component={PasswordExpirationView}
          path={`${match.url}/expired`}
        />
        <Route
          exact
          component={InactiveAllocationsView}
          path={`${match.url}/inactive`}
        />
        <Route
          path={`${match.url}/confirmation/:confirmationType/:confirmationToken?`}
          render={renderLoginConfirmation}
        />
        <Redirect to={match.url} />
      </Switch>

      <div className="login-info-message text-center mt-15">
        {loginPageMessage}
      </div>

      {termsConditionsInfo && !pendingTokenConfirmation ? (
        <LoginTermsConditionsModal
          {...termsConditionsInfo}
          onSubmit={handleConfirmAgreements}
        />
      ) : null}
    </>
  );
};

export default LoginPage;

const getTabLastPath = (user): string | void => {
  const restrictedModules = getRestrictedModules(user);
  const tabLastPathname = sessionStorage.getItem('TAB_LAST_PATHNAME');

  if (
    tabLastPathname &&
    !find(restrictedModules, ({ route }) =>
      startsWith(tabLastPathname, `/${route}`),
    )
  ) {
    return tabLastPathname;
  }
};

const getLastPath = (user): string => {
  const restrictedModules = getRestrictedModules(user);
  const tabLastPath = getTabLastPath(user);

  if (tabLastPath) {
    return tabLastPath;
  } else if (
    user.preferences?.LAST_PATHNAME &&
    !find(restrictedModules, ({ route }) =>
      startsWith(user.preferences.LAST_PATHNAME, `/${route}`),
    )
  ) {
    return user.preferences.LAST_PATHNAME;
  }

  // My Space module default route, hardcoded to avoid redundant module import
  return `/my-space/news`;
};
